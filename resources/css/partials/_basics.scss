/* ========================================
   Foundation Styles - Reset & Base Setup
   ======================================== */

@use 'colors' as *;

/* CSS Reset & Box Model */
* {
    box-sizing: border-box;
}

/* Base HTML & Body Setup */
html {
    font-size: 16px; // Base font size for rem calculations
    line-height: 1.5;
}

::selection {
    background-color: $brand-red-primary;
    color: rgb(255, 255, 255);
}

body {
    margin: 0;
    padding: 0;

    /* Typography Foundation */
    font-family: 'Inter', sans-serif;
    font-weight: 400;
    font-size: 1rem;
    line-height: 1.6;
    color: $text-primary;

    /* Font Rendering */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    /* Background */
    background-color: $bg-secondary;
}
section {
    padding: 8px;
}

/* Layout Container */
.container {
    max-width: 1440px;
    margin: 40px auto;
}

/* ========================================
   Typography System - Inter Font Scale
   Based on 1.25 (Major Third) scale with optical adjustments
   ======================================== */

/* Heading Hierarchy */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin: 0;

    /* Remove margin from last child */
    &:last-child {
        margin-bottom: 0;
    }
}

h1 {
    font-size: 2.25rem;   // 36px
    line-height: 1.2;
    font-weight: 700;
    letter-spacing: -0.01em;
    margin-bottom: 1.5rem;
}

h2 {
    font-size: 1.875rem;  // 30px
    line-height: 1.25;
    letter-spacing: -0.01em;
    margin-bottom: 1.25rem;
}

h3 {
    font-size: 1.5rem;    // 24px
    line-height: 1.3;
    letter-spacing: -0.005em;
    margin-bottom: 1rem;
}

h4 {
    font-size: 1.25rem;   // 20px
    line-height: 1.35;
    margin-bottom: 1rem;
}

h5 {
    font-size: 1.125rem;  // 18px
    line-height: 1.4;
    margin-bottom: 0.75rem;
}

h6 {
    font-size: 1rem;      // 16px
    line-height: 1.45;
    margin-bottom: 0.75rem;
}

/* Body Text */
p {
    font-size: 1rem;      // 16px
    line-height: 1.6;
    font-weight: 400;
    margin: 0 0 1rem 0;

    &:last-child {
        margin-bottom: 0;
    }
}

/* ========================================
   Responsive Typography Adjustments
   ======================================== */

/* Tablet & Mobile Adjustments */
@media (max-width: 768px) {
    h1 {
        font-size: 1.875rem;  // 30px on tablet/mobile
        line-height: 1.2;
    }

    h2 {
        font-size: 1.5rem;    // 24px on tablet/mobile
        line-height: 1.25;
    }

    h3 {
        font-size: 1.25rem;   // 20px on tablet/mobile
        line-height: 1.3;
    }

    h4 {
        font-size: 1.125rem;  // 18px on tablet/mobile
        line-height: 1.35;
    }
}

/* Small Mobile Adjustments */
@media (max-width: 480px) {
    h1 {
        font-size: 1.5rem;    // 24px on small mobile
        line-height: 1.25;
    }

    h2 {
        font-size: 1.25rem;   // 20px on small mobile
        line-height: 1.3;
    }

    h3 {
        font-size: 1.125rem;  // 18px on small mobile
        line-height: 1.35;
    }
}

/* ========================================
   Button System - Consistent Interactive Elements
   ======================================== */

/* Base Button Foundation */
button, .button {
    /* Layout & Positioning */
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;

    /* Visual Styling */
    border: none;
    border-radius: 20rem;
    background: none;
    outline: none;
    box-shadow: 0 2px 8px $shadow-medium;

    /* Typography */
    font-family: 'Inter', sans-serif;
    font-size: 1rem;
    font-weight: 700;
    text-decoration: none;

    /* Interaction */
    cursor: pointer;
    transform: translateY(0) scale(1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    /* Hover Effects */
    &:hover {
        transition: all 0.4s ease;
        box-shadow: 0 4px 12px $shadow-heavy;
    }

    /* Active State */
    &:active {
        box-shadow: 0 1px 4px $shadow-heavy;
        transition: all 0.1s ease;
    }

    /* Focus State */
    &:focus {
        outline: 2px solid rgba(1, 100, 73, 0.5);
        outline-offset: 2px;
    }

    /* Disabled State */
    &:disabled,
    &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none !important;

        &:hover {
            background-color: initial;
            color: initial;
        }
    }
}

/* ========================================
   Button Variants - Brand Color System
   ======================================== */

/* Primary Button - Brand Red */
.button-primary,
button.primary {
    background-color: $interactive-secondary;
    color: $text-inverse;

    &:hover {
        background-color: $interactive-primary;
        color: $text-inverse;
        box-shadow: 0 4px 12px $shadow-red,
                    0 8px 24px rgba($brand-red-primary, 0.15);
    }
}

/* Secondary Button - Brand Green */
.button-secondary,
button.secondary {
    background-color: $interactive-primary-hover;
    color: $interactive-primary;


    &:hover {
        background-color: $interactive-primary;
        color: $text-inverse;
        box-shadow: 0 4px 12px $shadow-green,
                    0 8px 24px rgba($brand-green-primary, 0.15);
    }
}

/* Menu CTA Button - Black with Red Hover */
.button-menu,
button.menu {
    background-color: $interactive-neutral;
    color: $text-inverse;

    &:hover {
        background-color: $interactive-neutral-hover;
        color: $text-inverse;
        box-shadow: 0 4px 12px rgba($brand-red-primary, 0.4),
                    0 8px 24px rgba($brand-red-primary, 0.2);
    }
}

/* ========================================
   Button Sizes & Modifiers
   ======================================== */

/* Size Variants */
.button-sm,
button.sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.button-lg,
button.lg {
    padding: 1rem 2rem;
    font-size: 1.125rem;
}

/* Outline Variants */
.button-outline {
    background-color: transparent;
    border: 2px solid $interactive-primary;
    color: $interactive-primary;
    box-shadow: none;

    &:hover {
        background-color: $interactive-primary;
        color: $text-inverse;
        box-shadow: 0 4px 12px $shadow-green,
                    0 8px 24px rgba($brand-green-primary, 0.15);
        border-color: $interactive-primary;
    }

    &:active {
        transform: translateY(0) scale(0.98);
        box-shadow: 0 1px 4px $shadow-heavy;
        transition: all 0.1s ease;
    }
}

.button-outline-primary {
    background-color: transparent;
    border: 2px solid #9C2B32;
    color: #9C2B32;
    box-shadow: none;

    &:hover {
        background-color: #9C2B32;
        color: white;
        box-shadow: 0 4px 12px rgba(156, 43, 50, 0.3),
                    0 8px 24px rgba(156, 43, 50, 0.15);
        border-color: #9C2B32;
    }
}

/* Layout Modifiers */
.button-full,
button.full {
    width: 100%;
}

.button-icon,
button.icon {
    gap: 0.5rem;
}

