/* Highlighted Updates Section - 2:1 Layout with responsive mobile-first approach */

@use 'colors' as *;
.highlighted-updates {
    display: grid;
    gap: 2rem;
    margin-bottom: 3rem;

    /* Mobile: Stack vertically with calendar first */
    grid-template-columns: 1fr;
    grid-template-areas:
        "calendar"
        "news";

    /* Desktop: Side by side with 2:1 ratio */
    @media (min-width: 768px) {
        grid-template-columns: 2fr 1fr;
        grid-template-areas: "news calendar";
    }

    h2 {
        grid-column: 1 / -1;
        margin-bottom: 1.5rem;
    }
}

/* News Section - Featured article with overlay */
.highlighted-updates-news {
    grid-area: news;

    article {
        position: relative;
        height: 100%;
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 4px 20px $shadow-light;
        transition: all 0.3s ease;

        &:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px $shadow-medium;
        }

        a {
            display: block;
            text-decoration: none;
            color: inherit;
            height: 100%;

            > div {
                position: relative;
                min-height: 300px;
                height: 100%;

                @media (min-width: 768px) {
                    min-height: 400px;
                }
            }

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                position: absolute;
                top: 0;
                left: 0;
            }

            /* Fallback for missing images */
            > div > div:first-child:not(.highlighted-news-content) {
                background: $brand-green-primary;
                width: 100%;
                height: 100%;
                position: absolute;
                top: 0;
                left: 0;
            }
        }
    }
}

/* News content overlay */
.highlighted-news-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, $bg-overlay);
    color: $text-inverse;
    padding: 2rem;

    time {
        display: block;
        font-size: 0.875rem;
        opacity: 0.9;
        margin-bottom: 0.5rem;
        font-weight: 500;
    }

    h2 {
        margin: 0;
        font-size: 1.5rem;
        line-height: 1.3;

        @media (min-width: 768px) {
            font-size: 1.875rem;
        }
    }

    p {
        margin: 0.75rem 0 0 0;
        opacity: 0.9;
        line-height: 1.5;
    }
}

/* Calendar Section - Stacked event layout */
.highlighted-updates-calendar {
    grid-area: calendar;

    .calendar-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;

        h3 {
            margin: 0;
            color: $text-brand;
        }

        a {
            color: $text-accent;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.875rem;

            &:hover {
                text-decoration: underline;
            }
        }
    }

    /* Events container - compact single container */
    .calendar-events {
        background: $bg-primary;
        border-radius: 12px;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    /* Individual event items - compact layout */
    .calendar-event {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem 0;
        text-decoration: none;
        color: inherit;
        border-bottom: 1px solid $border-light;
        transition: all 0.2s ease;

        &:last-child {
            border-bottom: none;
        }

        &:hover {
            background: rgba($brand-green-primary, 0.05);
            margin: 0 -1rem;
            padding-left: 1rem;
            padding-right: 1rem;
            border-radius: 8px;
        }
    }

    /* Date component with card-stack effect for multiple events */
    .event-date {
        flex-shrink: 0;
        width: 50px;
        height: 50px;
        background: $brand-green-primary;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: $text-inverse;
        position: relative;

        /* Card stack effect for multiple events */
        &.multiple-events {
            &::before {
                content: '';
                position: absolute;
                top: -3px;
                left: -3px;
                right: 3px;
                bottom: 3px;
                background: rgba($brand-green-primary, 0.3);
                border-radius: 8px;
                z-index: -1;
            }

            &::after {
                content: '';
                position: absolute;
                top: -6px;
                left: -6px;
                right: 6px;
                bottom: 6px;
                background: rgba($brand-green-primary, 0.15);
                border-radius: 8px;
                z-index: -2;
            }
        }

        span:first-child {
            font-size: 1.125rem;
            font-weight: 700;
            line-height: 1;
        }

        span:last-child {
            font-size: 0.625rem;
            font-weight: 500;
            text-transform: uppercase;
            opacity: 0.9;
        }
    }

    /* Event details */
    .event-details {
        flex: 1;
        min-width: 0;

        h4 {
            margin: 0 0 0.125rem 0;
            font-size: 0.875rem;
            font-weight: 600;
            color: $text-primary;
            line-height: 1.3;

            /* Truncate long titles */
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .event-meta {
            display: flex;
            gap: 0.5rem;

            span {
                font-size: 0.75rem;
                color: $text-secondary;

                &:first-child {
                    font-weight: 500;
                    color: $text-brand;
                }
            }
        }
    }

    /* "See full calendar" link - use secondary button */
    .calendar-footer {
        text-align: center;
        padding: 8px 0;
    }
}

/* News Carousel - Mobile-First Implementation */
.news-carousel {
    padding: 3rem 0;

    .container {
        max-width: 1440px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    /* Header with navigation */
    .carousel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;

        h3 {
            margin: 0;
            color: $text-brand;
            font-size: 1.5rem;
        }

        .carousel-nav {
            display: flex;
            flex-direction: row;
            gap: 0.5rem;

            @media (max-width: 640px) {
                display: none; /* Hide on mobile, rely on touch scrolling */
            }
        }
    }

    /* Navigation buttons - Ultra simple with SVG arrows */
    .carousel-btn {
        width: 44px;
        height: 44px;
        background: $bg-primary;
        border: none;
        border-radius: 50%;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background-color 0.2s ease;

        /* Override global button styles */
        padding: 0;
        box-shadow: none;
        transform: none;
        outline: none;

        &:hover:not(:disabled) {
            background-color: $interactive-primary-hover;
            color: $interactive-primary;
            box-shadow: none;
            transform: none;
        }

        &:active {
            box-shadow: none;
            transform: none;
        }

        &:focus {
            box-shadow: none;
            outline: none;
        }

        &:disabled {
            opacity: 0.4;
            cursor: not-allowed;
            box-shadow: none;
            transform: none;
        }

        /* SVG arrow icons */
        &::before {
            content: '';
            width: 16px;
            height: 16px;
            background-color: $color-black;
            transition: background-color 0.2s ease;
        }

        &:hover:not(:disabled)::before {
            background-color: $interactive-primary;
        }

        /* Previous arrow (left) */
        &--prev::before {
            mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='currentColor'%3E%3Cpath d='M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z'/%3E%3C/svg%3E") no-repeat center;
            mask-size: contain;
        }

        /* Next arrow (right) */
        &--next::before {
            mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='currentColor'%3E%3Cpath d='M8.59 16.59L10 18l6-6-6-6-1.41 1.41L13.17 12z'/%3E%3C/svg%3E") no-repeat center;
            mask-size: contain;
        }
    }

    /* Carousel wrapper - allows visible overflow for shadows/effects */
    .carousel-wrapper {
        position: relative;
        overflow: visible; /* Allow content to overflow for shadows/effects */
        padding: 1rem 0;
    }

    /* Scrollable container */
    .carousel-scroll {
        display: flex;
        gap: 1.5rem;
        overflow-x: auto;
        /* Note: overflow-y is automatically set to hidden when overflow-x is auto */
        scroll-behavior: smooth;
        scroll-snap-type: x mandatory;
        -webkit-overflow-scrolling: touch; /* iOS momentum scrolling */
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE/Edge */
        padding: 1rem;
        margin: -1rem;

        /* Hide scrollbar in WebKit browsers */
        &::-webkit-scrollbar {
            display: none;
        }

        @media (max-width: 640px) {
            gap: 1rem;
            padding: 1rem 0.5rem;
            margin: -1rem -0.5rem;
        }
    }

    /* Card base styles */
    .carousel-card {
        flex: 0 0 320px;
        background: $bg-primary;
        border-radius: 8px;

        box-shadow: 0 2px 12px $shadow-light;
        scroll-snap-align: start;
        transition: all 0.3s ease;

        @media (max-width: 768px) {
            flex: 0 0 280px;
        }

        @media (max-width: 480px) {
            flex: 0 0 260px;
        }

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba($color-black, 0.12);
        }

        a {
            display: block;
            text-decoration: none;
            color: inherit;
            height: 100%;
        }
    }

    /* Card image */
    .card-image {
        height: 200px;
        overflow: hidden;
        background: $gray-50;
        position: relative;

        @media (max-width: 480px) {
            height: 160px;
        }

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .card-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: $brand-green-primary;
            color: $text-inverse;

            svg {
                opacity: 0.6;
            }
        }
    }

    /* Card content */
    .card-content {
        padding: 1.25rem;

        @media (max-width: 480px) {
            padding: 1rem;
        }

        time {
            display: block;
            font-size: 0.75rem;
            color: $text-secondary;
            margin-bottom: 0.5rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        h4 {
            margin: 0 0 0.75rem 0;
            font-size: 1rem;
            font-weight: 600;
            line-height: 1.4;
            color: $text-primary;

            /* Limit to 2 lines */
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        p {
            margin: 0;
            font-size: 0.875rem;
            color: $text-secondary;
            line-height: 1.5;

            /* Limit to 3 lines */
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    }

    /* Archive card */
    .carousel-card--archive {
        background: $brand-red-primary;
        color: $text-inverse;

        .archive-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 2rem 1.5rem;
            height: 100%;
            min-height: 300px;

            @media (max-width: 480px) {
                padding: 1.5rem 1rem;
                min-height: 260px;
            }
        }

        .archive-icon {
            margin-bottom: 1rem;
            opacity: 0.9;

            svg {
                width: 48px;
                height: 48px;
            }
        }

        h4 {
            margin: 0 0 0.75rem 0;
            font-size: 1.25rem;
            font-weight: 600;
            color: $text-inverse;
        }

        p {
            margin: 0 0 1rem 0;
            font-size: 0.875rem;
            opacity: 0.9;
            line-height: 1.5;
            color: $text-inverse;
        }

        .archive-cta {
            font-size: 0.875rem;
            font-weight: 500;
            opacity: 0.8;
            color: $text-inverse;
        }
    }
}