/* ========================================
   OK Tyr Color System
   Brand colors and semantic color variables
   ======================================== */

/* ========================================
   Brand Colors - OK Tyr Identity
   ======================================== */

// Primary Brand Colors (A1-A4 range)
$brand-green-primary: #016449;      // Dominant brand color
$brand-green-light: #cfe7cb;        // Light green for hover states
$brand-green-dark: #014a37;         // Darker variant for emphasis

// Marketing/Accent Colors (B range)
$brand-red-primary: #9C2B32;        // Primary accent color
$brand-red-light: #b8434a;          // Lighter red variant
$brand-red-dark: #7a2228;           // Darker red variant

// Complementary Colors (C range) - for future use
$brand-blue: #2B5F9C;               // Complementary blue
$brand-orange: #D67E37;             // Complementary orange
$brand-purple: #6B2B9C;             // Complementary purple

/* ========================================
   Neutral Colors - Grays & Base Colors
   ======================================== */

// Pure colors
$color-white: #ffffff;
$color-black: #000000;

// Gray scale
$gray-50: #f8f9fa;                  // Lightest gray
$gray-100: #f5f5f5;                 // Very light gray
$gray-200: #eef2f6;                 // Light gray (body background)
$gray-300: #e0e0e0;                 // Light border gray
$gray-400: #ddd;                    // Border gray
$gray-500: #999;                    // Medium gray
$gray-600: #666;                    // Dark gray (secondary text)
$gray-700: #333;                    // Very dark gray
$gray-800: #1a1a1a;                 // Primary text color
$gray-900: #111;                    // Darkest gray

/* ========================================
   Semantic Color Variables
   ======================================== */

// Background colors
$bg-primary: $color-white;
$bg-secondary: $gray-200;
$bg-tertiary: $gray-100;
$bg-overlay: rgba($color-black, 0.6);

// Text colors
$text-primary: $gray-800;
$text-secondary: $gray-600;
$text-inverse: $color-white;
$text-brand: $brand-green-primary;
$text-accent: $brand-red-primary;

// Border colors
$border-light: $gray-300;
$border-medium: $gray-400;
$border-focus: $brand-green-primary;

// Interactive colors
$interactive-primary: $brand-green-primary;
$interactive-primary-hover: $brand-green-light;
$interactive-secondary: $brand-red-primary;
$interactive-secondary-hover: $brand-red-light;
$interactive-neutral: $color-black;
$interactive-neutral-hover: $brand-red-primary;

// State colors
$success: $brand-green-primary;
$warning: $brand-orange;
$error: $brand-red-primary;
$info: $brand-blue;

// Shadow colors
$shadow-light: rgba($color-black, 0.08);
$shadow-medium: rgba($color-black, 0.15);
$shadow-heavy: rgba($color-black, 0.25);

// Brand-specific shadows
$shadow-green: rgba($brand-green-primary, 0.3);
$shadow-red: rgba($brand-red-primary, 0.3);