/* ========================================
   Navigation System - Floating Frosted Glass Design
   ======================================== */

@use 'colors' as *;

/* Navigation Container - Fixed Positioning */
.navbar-container {
    /* Layout & Positioning */
    position: fixed;
    top: 16px;
    left: 0;
    right: 0;
    z-index: 100;

    /* Container Sizing */
    max-width: 1440px;
    width: 100%;
    margin: 0 auto;
    padding: 0 80px;

    /* Flexbox Layout */
    display: flex;
    justify-content: center;
    align-items: center;

    /* Smooth Show/Hide Animation */
    transform: translateY(0);
    transition: transform 0.4s ease-in-out;

    /* Navigation States */
    &.nav-hidden {
        transform: translateY(-200%);
    }

    &.nav-visible {
        transform: translateY(0);
    }
}

/* Main Navigation Bar */
.navbar {
    /* Layout & Positioning */
    position: relative;
    width: 100%;
    max-width: 1250px;
    display: flex;
    align-items: center;
    padding: 16px;

    /* Visual Styling */
    border-radius: 10rem;
    color: white;
    transition: all 0.4s ease;

    /* Fallback Background (for unsupported browsers) */
    background: rgba(1, 100, 73, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

    /* Mobile Expanded State */
    &.nav-mobile-expanded {
        /* Layout Changes */
        border-radius: 1.5rem;
        flex-direction: column;
        align-items: stretch;
        height: 100vh;
        max-width: none;
        width: 100%;
        margin: 0;
        padding: 0;
        overflow: hidden;
        transform-origin: center top;

        /* Header Elements Positioning */
        .nav-logo,
        .nav-mobile-toggle {
            position: absolute;
            z-index: 10;
        }

        .nav-logo {
            top: 16px;
            left: 16px;
        }

        .nav-mobile-toggle {
            top: 16px;
            right: 16px;
        }

        /* Mobile Content Area */
        .nav-mobile-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            height: 100%;
            padding: 4rem 1rem 1rem;
            overflow-y: auto;
        }

        .nav-mobile-items {
            flex: 1;
        }
    }
}

/* ========================================
   Frosted Glass Effect - Enhanced Visual Design
   ======================================== */

@supports (backdrop-filter: blur(16px)) or (-webkit-backdrop-filter: blur(16px)) {
    .navbar {
        /* Enhanced Background with Brand Tint */
        background: rgba(1, 100, 73, 0.6);
        border: 1px solid rgba(255, 255, 255, 0.1);

        /* Inset Border Effect */
        -webkit-box-shadow: inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1);
        -moz-box-shadow: inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1);
        box-shadow: inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1),
                    0 2px 8px rgba(0, 0, 0, 0.15);

        /* Frosted Glass Backdrop Filter */
        -webkit-backdrop-filter: blur(16px) saturate(140%) brightness(90%);
        backdrop-filter: blur(16px) saturate(140%) brightness(90%);

        /* Enhanced Mobile Expanded State */
        &.nav-mobile-expanded {
            background: rgba(0, 0, 0, 0.6);
            -webkit-backdrop-filter: blur(20px) saturate(150%) brightness(80%);
            backdrop-filter: blur(20px) saturate(150%) brightness(80%);
        }
    }
}

/* ========================================
   Navigation Content Layout
   ======================================== */

/* Logo Section */
.nav-logo {
    flex: 0 0 auto;
    max-width: 40px;
    max-height: 40px;

    img {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }
}

/* Navigation Items Collection */
.nav-collection {
    display: flex;
    align-items: center;
    flex: 1 1 auto;
    justify-content: center;
}

/* Call-to-Action Section */
.nav-cta {
    flex: 0 0 auto;

    a {
        /* Button Menu Styles */
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background-color: $interactive-neutral;
        color: $text-inverse;
        padding: 8px 16px;
        border: none;
        border-radius: 20rem;
        font-family: 'Inter', sans-serif;
        font-size: 1rem;
        font-weight: 500;
        text-decoration: none;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 2px 8px $shadow-medium;
        transform: translateY(0) scale(1);

        &:hover {
            background-color: $interactive-neutral-hover;
            color: $text-inverse;
            transform: translateY(-1px) scale(1.02);
            box-shadow: 0 4px 12px rgba($brand-red-primary, 0.4),
                        0 8px 24px rgba($brand-red-primary, 0.2);
        }

        &:active {
            transform: translateY(0) scale(0.98);
            box-shadow: 0 1px 4px $shadow-heavy;
            transition: all 0.1s ease;
        }

        &:focus {
            outline: 2px solid rgba(1, 100, 73, 0.5);
            outline-offset: 2px;
        }
    }
}

/* Navigation Item Container */
.nav-item {
    position: relative;
}

/* Override Global Button Styles for Navbar */
.navbar button {
    box-shadow: none;
}

/* ========================================
   Navigation Links & Interactive Elements
   ======================================== */

/* Base Navigation Links & Toggles */
.nav-link,
.nav-toggle {
    /* Layout */
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;

    /* Reset Styles */
    background: none;
    border: none;
    text-decoration: none;
    color: inherit;

    /* Typography */
    font-size: 1rem;
    font-weight: 500;

    /* Visual */
    border-radius: 0.5rem;
    cursor: pointer;
    transition: background-color 0.2s ease;

    /* Override Global Button Styles */
    box-shadow: none !important;
    transform: none !important;

    /* Hover State */
    &:hover {
        background-color: rgba(255, 255, 255, 0.1);
        transform: none !important;
        box-shadow: none !important;
    }

    /* Active State */
    &:active {
        transform: none !important;
        box-shadow: none !important;
    }

    /* Focus State */
    &:focus {
        outline: 2px solid rgba(255, 255, 255, 0.5);
        outline-offset: 2px;
    }
}

// Arrow animation for dropdowns (chevron to minus)
.nav-toggle,
.nav-toggle-level2 {
    .nav-arrow {
        transition: all 0.3s ease;
        opacity: 1;
    }

    // Hide chevron and show minus when expanded
    &[aria-expanded="true"] {
        .nav-arrow {
            opacity: 0;
            transform: scale(0.8);
        }

        // Add minus icon after chevron fades out
        &::after {
            content: '';
            position: absolute;
            width: 10px;
            height: 2px;
            background-color: currentColor;
            border-radius: 1px;
            opacity: 1;
            transform: scale(1);
            transition: all 0.3s ease 0.1s;
            right: 0.5rem; // Position for desktop dropdowns
        }
    }

    // Initially hide the minus icon
    &::after {
        content: '';
        position: absolute;
        width: 10px;
        height: 2px;
        background-color: currentColor;
        border-radius: 1px;
        opacity: 0;
        transform: scale(0.8);
        transition: all 0.3s ease;
        right: 0.5rem; // Position for desktop dropdowns
    }
}

// Dropdown containers
.nav-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 200px;
    background: rgba(0, 0, 0, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
    padding: 0.5rem 0;
    margin-top: 0.5rem;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
    z-index: 1000;

    // Backdrop filter for supported browsers
    @supports (backdrop-filter: blur(16px)) or (-webkit-backdrop-filter: blur(16px)) {
        -webkit-backdrop-filter: blur(16px) saturate(140%) brightness(90%);
        backdrop-filter: blur(16px) saturate(140%) brightness(90%);
    }

    // Show dropdown when parent is active
    .nav-item:hover &,
    .nav-item.nav-active & {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }
}

// Level 2 dropdown positioning
.nav-dropdown-level2 {
    top: 0;
    left: 100%;
    margin-top: 0;
    margin-left: 0.5rem;
}

// Dropdown items
.nav-dropdown-item {
    position: relative;
}

.nav-dropdown-link,
.nav-toggle-level2 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0.75rem 1rem;
    text-decoration: none;
    color: inherit;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s ease;

    // Override global button styles
    box-shadow: none !important;
    transform: none !important;
    border-radius: 0;
    font-weight: inherit;
    font-family: inherit;

    &:hover {
        background-color: rgba(255, 255, 255, 0.1);
        // Override global button hover effects
        transform: none !important;
        box-shadow: none !important;
    }

    &:active {
        // Override global button active effects
        transform: none !important;
        box-shadow: none !important;
    }

    &:focus {
        outline: 2px solid rgba(255, 255, 255, 0.5);
        outline-offset: -2px;
        background-color: rgba(255, 255, 255, 0.1);
    }
}

/* This section is now handled above in the reorganized .nav-cta section */

// Mobile Menu Toggle Button
.nav-mobile-toggle {
    display: none;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: none;
    cursor: pointer;
    color: inherit;
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: background-color 0.2s ease;
    margin-left: auto; // Push to the right side

    // Override global button styles
    box-shadow: none !important;
    transform: none !important;
    font-size: inherit;
    font-weight: inherit;
    font-family: inherit;

    &:hover {
        background-color: rgba(255, 255, 255, 0.1);
        // Override global button hover effects
        transform: none !important;
        box-shadow: none !important;
    }

    &:active {
        // Override global button active effects
        transform: none !important;
        box-shadow: none !important;
    }

    &:focus {
        outline: 2px solid rgba(255, 255, 255, 0.5);
        outline-offset: 2px;
    }
}

.hamburger-icon {
    display: flex;
    flex-direction: column;
    gap: 3px;
    width: 18px;
    height: 14px;
    order: 2; // Place icon after text
}

.hamburger-line {
    width: 100%;
    height: 2px;
    background-color: currentColor;
    border-radius: 1px;
    transition: all 0.3s ease;
}

.nav-mobile-toggle-text {
    font-size: 0.9rem;
    font-weight: 500;
    order: 1; // Place text before icon
}

// Hamburger animation when menu is open (hamburger to minus)
.nav-mobile-toggle[aria-expanded="true"] {
    .hamburger-line {
        &:nth-child(1) {
            opacity: 0;
            transform: translateY(5px);
        }
        &:nth-child(2) {
            // Middle line becomes the minus
            transform: scaleX(1);
        }
        &:nth-child(3) {
            opacity: 0;
            transform: translateY(-5px);
        }
    }
}

// Mobile Menu Content (inside expanded nav)
.nav-mobile-content {
    display: none;
    flex-direction: column;
    flex: 1;
    width: 100%;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.4s ease 0.2s; // Delay for smooth entrance

    // Show when nav is expanded
    .nav-mobile-expanded & {
        display: flex;
        opacity: 1;
        transform: translateY(0);
    }
}

.nav-mobile-items {
    flex: 1;
    overflow-y: auto;
}

// Mobile Menu Items
.nav-mobile-item {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 0 1rem;
    opacity: 0;
    transform: translateY(10px);
    animation: none; // Reset animation initially

    // Apply animation when nav is expanded
    .nav-mobile-expanded & {
        animation: fadeInUp 0.4s ease forwards;

        // Stagger animation for each item
        &:nth-child(1) { animation-delay: 0.3s; }
        &:nth-child(2) { animation-delay: 0.4s; }
        &:nth-child(3) { animation-delay: 0.5s; }
        &:nth-child(4) { animation-delay: 0.6s; }
        &:nth-child(5) { animation-delay: 0.7s; }
        &:nth-child(6) { animation-delay: 0.8s; }
        &:nth-child(7) { animation-delay: 0.9s; }
        &:nth-child(8) { animation-delay: 1.0s; }
    }
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

// Split header for items with submenus
.nav-mobile-item-header,
.nav-mobile-subitem-header {
    display: flex;
    align-items: center;
    width: 100%;
}

.nav-mobile-item-header .nav-mobile-link,
.nav-mobile-subitem-header .nav-mobile-link {
    flex: 1;
    padding: 1rem 0;
    text-decoration: none;
    color: inherit;
    font-size: 1.1rem;
    font-weight: 500;
    transition: color 0.2s ease;

    &:hover {
        color: rgba(255, 255, 255, 0.8);
    }

    &:focus {
        outline: 2px solid rgba(255, 255, 255, 0.5);
        outline-offset: 2px;
    }
}

// Toggle buttons for submenus
.nav-mobile-toggle-item,
.nav-mobile-toggle-subitem {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: none;
    border: none;
    cursor: pointer;
    color: inherit;
    border-radius: 0.5rem;
    transition: background-color 0.2s ease;

    // Override global button styles
    padding: 0 !important;
    box-shadow: none !important;
    transform: none !important;
    font-size: inherit;
    font-weight: inherit;
    font-family: inherit;

    &:hover {
        background-color: rgba(255, 255, 255, 0.1);
        // Override global button hover effects
        transform: none !important;
        box-shadow: none !important;
    }

    &:active {
        // Override global button active effects
        transform: none !important;
        box-shadow: none !important;
    }

    &:focus {
        outline: 2px solid rgba(255, 255, 255, 0.5);
        outline-offset: 2px;
    }
}

// Icon swap animation: chevron to minus (active)
.nav-mobile-toggle-item,
.nav-mobile-toggle-subitem {
    .nav-arrow {
        transition: all 0.3s ease;
        opacity: 1;
    }

    // Hide chevron and show minus when expanded
    &[aria-expanded="true"] {
        .nav-arrow {
            opacity: 0;
            transform: scale(0.8);
        }

        // Add minus icon after chevron fades out
        &::after {
            content: '';
            position: absolute;
            width: 12px;
            height: 2px;
            background-color: currentColor;
            border-radius: 1px;
            opacity: 1;
            transform: scale(1);
            transition: all 0.3s ease 0.1s; // Slight delay for smooth transition
        }
    }

    // Initially hide the minus icon
    &::after {
        content: '';
        position: absolute;
        width: 12px;
        height: 2px;
        background-color: currentColor;
        border-radius: 1px;
        opacity: 0;
        transform: scale(0.8);
        transition: all 0.3s ease;
    }
}


// Regular mobile links (no submenus)
.nav-mobile-link {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 1rem 0;
    text-decoration: none;
    color: inherit;
    font-size: 1.1rem;
    font-weight: 500;
    transition: color 0.2s ease;

    &:hover {
        color: rgba(255, 255, 255, 0.8);
    }

    &:focus {
        outline: 2px solid rgba(255, 255, 255, 0.5);
        outline-offset: 2px;
    }
}

.nav-mobile-link-level1 {
    font-size: 1.2rem;
    font-weight: 600;
}

.nav-mobile-link-level2,
.nav-mobile-subitem-header .nav-mobile-link {
    padding-left: 1rem;
    font-size: 1rem;
    font-weight: 400;
}

.nav-mobile-link-level3 {
    padding-left: 2rem;
    font-size: 0.9rem;
    font-weight: 400;
}

// Mobile Submenus
.nav-mobile-submenu,
.nav-mobile-subsubmenu {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;

    &.nav-mobile-submenu-active {
        max-height: 500px; // Adjust based on content
    }
}

.nav-mobile-subitem {
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

// Mobile CTA at bottom
.nav-mobile-cta {
    margin-top: auto;
    padding: 1rem;
    width: 100%;
    opacity: 0;
    text-align: center;
    transform: translateY(20px);
    animation: none; // Reset animation initially

    // Apply animation when nav is expanded
    .nav-mobile-expanded & {
        animation: fadeInUp 0.4s ease 1.1s forwards; // Animate in last
    }
}

.nav-mobile-cta-button {
    /* Button Menu + Full Width Styles */
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: #000000;
    color: white;
    padding: 16px 24px;
    border: none;
    border-radius: 20rem;
    font-family: 'Inter', sans-serif;
    font-size: 1rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(0) scale(1);
    width: 100%;

    &:hover {
        background-color: #9C2B32;
        color: white;
        transform: translateY(-1px) scale(1.02);
        box-shadow: 0 4px 12px rgba(156, 43, 50, 0.4),
                    0 8px 24px rgba(156, 43, 50, 0.2);
    }

    &:active {
        transform: translateY(0) scale(0.98);
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
        transition: all 0.1s ease;
    }

    &:focus {
        outline: 2px solid rgba(1, 100, 73, 0.5);
        outline-offset: 2px;
    }
}

//Decrease padding after 1600px
@media (max-width: 1600px) {
    .navbar-container {
        padding: 0 20px;
    }
}

//Remove CTA after 1200px
@media (max-width: 1200px) {
    .nav-cta {
        display: none;
    }
}

// Mobile navigation breakpoint
@media (max-width: 1024px) {
    .nav-desktop {
        display: none;
    }

    .nav-mobile-toggle {
        display: flex;
    }

    // Ensure proper flex layout in mobile mode
    .navbar {
        justify-content: space-between;

        .nav-logo {
            flex: 0 0 auto;
        }

        // When expanded, take full viewport
        &.nav-mobile-expanded {
            position: fixed;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            max-width: none;
            width: auto;
            height: auto;
            z-index: 1000;
        }
    }

    // Adjust container for expanded state
    .navbar-container {
        &.nav-expanded {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            padding: 20px;
            z-index: 1000;
        }
    }
}

@media (max-width: 768px) {
    .navbar-container {
        padding: 0 20px; // Reduced padding on mobile
    }

    nav {
        padding: 12px; // Reduced nav padding on mobile
    }
}

// Enhanced hover effects for desktop
@media (min-width: 769px) {
    .nav-item {
        // Hover to show dropdown
        &:hover .nav-dropdown {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        // Nested hover for level 2
        .nav-dropdown-item:hover .nav-dropdown-level2 {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
    }
}

// Animation improvements
.nav-dropdown {
    // Smooth entrance animation
    animation-duration: 0.2s;
    animation-timing-function: ease-out;
    animation-fill-mode: both;

    &.nav-dropdown-level2 {
        animation-delay: 0.1s; // Slight delay for nested dropdowns
    }
}

// Focus management for accessibility
.nav-toggle:focus,
.nav-toggle-level2:focus,
.nav-dropdown-link:focus {
    outline: 2px solid rgba(255, 255, 255, 0.8);
    outline-offset: 2px;
    background-color: rgba(255, 255, 255, 0.15);
}