/* ========================================
   Hero Section - Main landing area
   ======================================== */

@use 'colors' as *;

.hero {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    h1 {
        font-size: 3em;
    }
    @media (max-width: 768px) {
        h1 {
            font-size: 2em;
        }
    }

    /* Hero Content Container */
    &-content {
        height: 90vh;
        max-height: 800px;
        min-height: 600px;
        max-width: 1440px;
        width: 100%;
        position: relative;
        overflow: hidden;
        border-radius: 24px;
        padding: 2rem;

        /* Layout */
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        align-items: flex-start;

        /* Background & Visual Effects */
        background-size: cover !important;
        background-position: center center;
        background: linear-gradient(180deg, rgba($color-black, 0) 0%, $bg-overlay 100%),
                    linear-gradient(0deg, $bg-overlay, $bg-overlay),
                    linear-gradient(0deg, $brand-green-primary, $brand-green-primary);
        background-blend-mode: normal, normal, color, normal, normal;

        /* Typography */
        color: $text-inverse;
    }

    /* Call-to-Action Section */
    &-cta {
        margin-top: 2rem;
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }
}

/* ========================================
   Banner Content Area
   ======================================== */

.banner {
    width: 50vw;
    padding: 2rem;

    /* Typography Styles - inherit from global styles */

    /* Mobile Responsive */
    @media (max-width: 768px) {
        width: 100%;
        padding: 0;
    }
}

